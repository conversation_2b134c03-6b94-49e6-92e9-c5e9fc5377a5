"""
LLM Factory for creating different LLM instances with proper configuration.
Supports multiple providers: Google Gemini, OpenAI, Anthropic, etc.
Handles provider-specific configurations and deprecation warnings.
"""

from typing import Dict, Any, Optional
from abc import ABC, abstractmethod
import warnings
from pathlib import Path


class LLMProvider(ABC):
    """Abstract base class for LLM providers."""

    @abstractmethod
    def create_llm(self, config: Dict[str, Any]) -> Any:
        """Create and return an LLM instance."""
        pass

    @abstractmethod
    def get_supported_models(self) -> list:
        """Return list of supported model names."""
        pass


class LLMFactory:
    """Factory class for creating LLM instances."""

    def __init__(self):
        self.providers = {
            'google': GoogleGeminiProvider(),
            'openai': OpenAIProvider(),
            'anthropic': AnthropicProvider(),
        }

        # Model to provider mapping
        self.model_to_provider = {}
        for provider_name, provider in self.providers.items():
            for model in provider.get_supported_models():
                self.model_to_provider[model] = provider_name

    def create_llm(self, config: Dict[str, Any]) -> Any:
        """
        Create an LLM instance based on the model name in config.

        Args:
            config: Dictionary containing model_name and other LLM parameters

        Returns:
            LLM instance

        Raises:
            ValueError: If model is not supported
            ImportError: If required package is not installed
        """
        model_name = config.get('model_name')
        if not model_name:
            raise ValueError("model_name is required in config")

        # Determine provider from model name
        provider_name = self.model_to_provider.get(model_name)
        if not provider_name:
            raise ValueError(
                f"Unsupported model: {model_name}. Supported models: {list(self.model_to_provider.keys())}")

        provider = self.providers[provider_name]
        return provider.create_llm(config)

    def get_supported_models(self) -> Dict[str, list]:
        """Get all supported models grouped by provider."""
        return {name: provider.get_supported_models() for name, provider in self.providers.items()}

    def add_provider(self, name: str, provider: LLMProvider):
        """Add a custom LLM provider."""
        self.providers[name] = provider
        # Update model mapping
        for model in provider.get_supported_models():
            self.model_to_provider[model] = name


class GoogleGeminiProvider(LLMProvider):
    """Google Gemini LLM provider."""

    def create_llm(self, config: Dict[str, Any]) -> Any:
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
        except ImportError:
            raise ImportError(
                "langchain-google-genai is required for Google Gemini models")

        # Handle the deprecation warning by removing the deprecated parameter
        llm_config = {
            'model': config['model_name'],
            'temperature': config.get('temperature', 0.0),
        }

        # Add other Google-specific configurations if needed
        if 'max_tokens' in config:
            llm_config['max_output_tokens'] = config['max_tokens']

        # Note: convert_system_message_to_human is deprecated, so we don't use it
        # Instead, we'll handle system messages in the prompt template

        return ChatGoogleGenerativeAI(**llm_config)

    def get_supported_models(self) -> list:
        return [
            'gemini-2.0-flash',
            'gemini-1.5-pro',
            'gemini-1.5-flash',
            'gemini-pro',
            'gemini-pro-vision'
        ]


class OpenAIProvider(LLMProvider):
    """OpenAI LLM provider."""

    def create_llm(self, config: Dict[str, Any]) -> Any:
        try:
            from langchain_openai import ChatOpenAI
        except ImportError:
            raise ImportError("langchain-openai is required for OpenAI models")

        llm_config = {
            'model': config['model_name'],
            'temperature': config.get('temperature', 0.0),
        }

        if 'max_tokens' in config:
            llm_config['max_tokens'] = config['max_tokens']

        if 'api_key' in config:
            llm_config['api_key'] = config['api_key']

        return ChatOpenAI(**llm_config)

    def get_supported_models(self) -> list:
        return [
            'gpt-4o',
            'gpt-4o-mini',
            'gpt-4-turbo',
            'gpt-4',
            'gpt-3.5-turbo'
        ]


class AnthropicProvider(LLMProvider):
    """Anthropic Claude LLM provider."""

    def create_llm(self, config: Dict[str, Any]) -> Any:
        try:
            from langchain_anthropic import ChatAnthropic
        except ImportError:
            raise ImportError(
                "langchain-anthropic is required for Anthropic models")

        llm_config = {
            'model': config['model_name'],
            'temperature': config.get('temperature', 0.0),
        }

        if 'max_tokens' in config:
            llm_config['max_tokens'] = config['max_tokens']

        if 'api_key' in config:
            llm_config['api_key'] = config['api_key']

        return ChatAnthropic(**llm_config)

    def get_supported_models(self) -> list:
        return [
            'claude-3-5-sonnet-20241022',
            'claude-3-5-haiku-20241022',
            'claude-3-opus-20240229',
            'claude-3-sonnet-20240229',
            'claude-3-haiku-20240307'
        ]


# Global factory instance
llm_factory = LLMFactory()


def create_llm(config: Dict[str, Any]) -> Any:
    """
    Convenience function to create an LLM instance.

    Args:
        config: Dictionary containing model_name and other LLM parameters

    Returns:
        LLM instance

    Example:
        config = {
            'model_name': 'gemini-2.0-flash',
            'temperature': 0.1
        }
        llm = create_llm(config)
    """
    return llm_factory.create_llm(config)


def get_supported_models() -> Dict[str, list]:
    """Get all supported models grouped by provider."""
    return llm_factory.get_supported_models()
