#!/usr/bin/env python3
"""
Test script to verify the improved SQLJudge JSON parsing capabilities.
"""

from config_utils import load_full_config
from sql_judge_agent.judge import SQLJudge, SQLEvaluation
import sys
from pathlib import Path

# Add root directory to Python path for clean imports
sys.path.insert(0, str(Path(__file__).parent))


def test_json_parsing():
    """Test the improved JSON parsing functionality."""

    # Load configuration
    full_config = load_full_config()
    config = full_config['judge_llm']

    # Create SQLJudge instance
    judge = SQLJudge(config)

    # Test data
    schema = """
    CREATE TABLE employees (
        id INT PRIMARY KEY,
        name VARCHAR(100),
        department VARCHAR(50),
        salary DECIMAL(10,2)
    );
    """

    user_prompt = "Show me all employees in the IT department"
    generated_sql = "SELECT * FROM employees WHERE department = 'IT';"

    print("🧪 Testing improved SQLJudge JSON parsing...")
    print(f"📊 Using model: {config.get('model_name', 'unknown')}")
    print("-" * 60)

    try:
        # Run evaluation
        result = judge.evaluate(schema, user_prompt, generated_sql)

        print("✅ Evaluation completed successfully!")
        print(f"📋 Result type: {type(result)}")
        print(f"🎯 Final verdict: {result.final_verdict}")
        print(f"📊 Correctness score: {result.correctness_score}/5")
        print(f"⚡ Optimization score: {result.optimization_score}/5")
        print(f"🔒 Is safe: {result.is_safe}")
        print("-" * 60)
        print("📝 Detailed reasoning:")
        print(f"   Correctness: {result.correctness_reasoning[:100]}...")
        print(f"   Optimization: {result.optimization_reasoning[:100]}...")
        print(f"   Security: {result.security_reasoning[:100]}...")

        return True

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_json_parsing_methods():
    """Test the individual JSON parsing methods."""

    # Load configuration
    full_config = load_full_config()
    config = full_config['judge_llm']
    judge = SQLJudge(config)

    print("\n🔧 Testing individual JSON parsing methods...")
    print("-" * 60)

    # Test case 1: Well-formed JSON in markdown
    test_response_1 = '''
    Here's the evaluation:
    ```json
    {
        "correctness_score": 5,
        "correctness_reasoning": "The query correctly selects all columns from employees table with proper WHERE clause",
        "optimization_score": 4,
        "optimization_reasoning": "Query is efficient with indexed WHERE clause",
        "is_safe": true,
        "security_reasoning": "Safe SELECT query with no destructive operations",
        "final_verdict": "PASS"
    }
    ```
    '''

    # Test case 2: JSON without markdown
    test_response_2 = '''
    {
        "correctness_score": 3,
        "correctness_reasoning": "Query has some issues",
        "optimization_score": 2,
        "optimization_reasoning": "Could be optimized better",
        "is_safe": true,
        "security_reasoning": "No security issues found",
        "final_verdict": "FAIL"
    }
    '''

    # Test case 3: Malformed response that should trigger regex parsing
    test_response_3 = '''
    The evaluation shows:
    correctness_score: 4
    correctness_reasoning: "Good query structure"
    optimization_score: 3
    optimization_reasoning: "Decent performance"
    is_safe: true
    security_reasoning: "No threats detected"
    final_verdict: "PASS"
    '''

    test_cases = [
        ("Markdown JSON", test_response_1),
        ("Plain JSON", test_response_2),
        ("Regex extraction", test_response_3)
    ]

    for test_name, response in test_cases:
        print(f"\n🧪 Testing {test_name}:")
        try:
            result = judge._parse_json_response(response)
            print(
                f"   ✅ Parsed successfully: {result.final_verdict} (score: {result.correctness_score})")
        except Exception as e:
            print(f"   ❌ Failed: {e}")

    return True


if __name__ == "__main__":
    print("🚀 Starting SQLJudge improvement tests...")

    # Test main evaluation function
    success1 = test_json_parsing()

    # Test individual parsing methods
    success2 = test_json_parsing_methods()

    if success1 and success2:
        print("\n🎉 All tests completed successfully!")
        print("✨ The improved SQLJudge is ready to use!")
    else:
        print("\n⚠️  Some tests failed. Please check the output above.")
        sys.exit(1)
