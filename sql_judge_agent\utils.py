# sql_evaluater/utils.py
import os
from typing import Dict, Any, List
from sqlalchemy import create_engine, inspect, MetaData, Table
from sqlalchemy.engine import Engine
from pathlib import Path


def get_db_schema(db_config: Dict[str, Any]) -> str:
    """
    Connects to a PostgreSQL database and fetches the schema of all tables using SQLAlchemy.

    This function now searches across all schemas (not just 'public') to find tables
    and returns formatted CREATE TABLE statements for the LLM.

    Args:
        db_config: A dictionary with database connection details.

    Returns:
        A string containing formatted 'CREATE TABLE' statements for the LLM.
    """
    engine = None
    try:
        # Create SQLAlchemy engine
        connection_string = (
            f"postgresql://{db_config['user']}:{db_config['password']}"
            f"@{db_config['host']}:{db_config['port']}/{db_config['database']}"
        )
        engine = create_engine(connection_string)

        # Create inspector to examine database structure
        inspector = inspect(engine)

        # Get all schema names
        schema_names = inspector.get_schema_names()
        print(f"📋 Found schemas: {schema_names}")

        schema_parts: List[str] = []
        total_tables = 0

        # Iterate through all schemas
        for schema_name in schema_names:
            # Skip system schemas
            if schema_name in ['information_schema', 'pg_catalog', 'pg_toast']:
                continue

            # Get table names in this schema
            table_names = inspector.get_table_names(schema=schema_name)

            if not table_names:
                continue

            print(
                f"📊 Processing schema '{schema_name}' with {len(table_names)} tables")

            for table_name in table_names:
                try:
                    # Get column information for the table
                    columns = inspector.get_columns(
                        table_name, schema=schema_name)

                    if not columns:
                        continue

                    # Format column definitions
                    col_defs = []
                    for column in columns:
                        col_name = column['name']
                        col_type = str(column['type'])
                        col_defs.append(f"  {col_name} {col_type}")

                    # Create the CREATE TABLE statement
                    full_table_name = f"{schema_name}.{table_name}" if schema_name != 'public' else table_name
                    create_statement = f"CREATE TABLE {full_table_name} (\n" + ",\n".join(
                        col_defs) + "\n);"
                    schema_parts.append(create_statement)
                    total_tables += 1

                except Exception as e:
                    print(
                        f"⚠️  Warning: Could not process table {schema_name}.{table_name}: {e}")
                    continue

        if schema_parts:
            result = "\n\n".join(schema_parts)
            print(
                f"✅ Successfully fetched database schema for {total_tables} tables.")
            print("📋 Schema preview:")
            print(result[:500] + "..." if len(result) > 500 else result)
            return result
        else:
            print("⚠️  No tables found in any schema.")
            return ""

    except Exception as e:
        print(f"❌ Database Error: {e}")
        return ""
    finally:
        if engine:
            engine.dispose()


if __name__ == "__main__":
    import sys
    from pathlib import Path

    # Add root directory to Python path for clean imports
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from config_utils import load_db_config

    db_config = load_db_config('yaml')
    get_db_schema(db_config)
